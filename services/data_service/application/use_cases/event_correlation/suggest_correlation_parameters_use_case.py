from datetime import datetime
from typing import Literal
from zoneinfo import ZoneInfo

from pydantic import Field, model_validator
from pydantic_ai import Agent
from pydantic_ai.models import Model

from services.base.application.utils.time import TimeUtils
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.query.aggregations import (
    SimpleAggregationMethod,
)
from services.base.domain.schemas.shared import BaseDataModel
from services.base.type_resolver import TypeResolver
from services.data_service.api.queries.event_query_api import EventTypedQueryAPI
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    CorrelationTemporalOptions,
    CorrelationVariableInput,
    EnvironmentTypedQuery,
)
from services.data_service.application.use_cases.event_correlation.suggest_correlation_parameters_prompt import (
    SUGGEST_EVENT_CORRELATION_PROMPT,
)


class SuggestCorrelationParametersUseCaseInputBoundary(BaseDataModel):
    dependent_query: EventTypedQueryAPI | EnvironmentTypedQuery
    independent_query: EventTypedQueryAPI | EnvironmentTypedQuery


class CorrelateVariableAIOutput(BaseDataModel):
    field_name: NonEmptyStr | None
    aggregation_method: SimpleAggregationMethod | None

    @model_validator(mode="after")
    def validate_query(self):
        if self.aggregation_method and not self.field_name:
            raise ValueError("Field name must be specified for aggregation")
        return self


class CorrelationTemporalOptionsAIOutput(BaseDataModel):
    time_delta: NonEmptyStr = Field(
        description="exposes the suggested time range of the correlation. Use months or years, like 1y, 3m."
    )
    type: Literal["before", "after", "closest"]
    delta_from: NonEmptyStr = Field(description="Use hours or days, like 1d, 3h.")
    delta_to: NonEmptyStr = Field(description="Use hours or days, like 1d, 3h.")


class SuggestEventCorrelationAIOutput(BaseDataModel):
    temporal_options: CorrelationTemporalOptionsAIOutput
    dependent: CorrelateVariableAIOutput
    independent: CorrelateVariableAIOutput
    reasoning: NonEmptyStr


class SuggestEventCorrelationUseCaseOutputBoundary(BaseDataModel):
    dependent: CorrelationVariableInput
    independent: CorrelationVariableInput
    temporal_options: CorrelationTemporalOptions
    reasoning: str


class SuggestCorrelationParametersUseCase:
    def __init__(self, model: Model):
        self._prompt = SUGGEST_EVENT_CORRELATION_PROMPT.format(
            input_schema=SuggestCorrelationParametersUseCaseInputBoundary.model_json_schema(),
            output_schema=SuggestEventCorrelationAIOutput.model_json_schema(),
        )
        self._model = model

    async def execute_async(
        self,
        input_boundary: SuggestCorrelationParametersUseCaseInputBoundary,
    ) -> SuggestEventCorrelationUseCaseOutputBoundary | None:

        if isinstance(input_boundary.dependent_query, EventTypedQueryAPI):
            dependent_types_schemas = [
                TypeResolver.get_document(t).model_json_schema() for t in input_boundary.dependent_query.types
            ]
        elif isinstance(input_boundary.dependent_query, EnvironmentTypedQuery):
            dependent_types_schemas = [
                TypeResolver.get_document(input_boundary.dependent_query.domain_type).model_json_schema()
            ]
        else:
            raise ShouldNotReachHereException(f"Unsupported query type {input_boundary.dependent_query}")
        if isinstance(input_boundary.independent_query, EventTypedQueryAPI):
            independent_types_schemas = [
                TypeResolver.get_document(t).model_json_schema() for t in input_boundary.independent_query.types
            ]
        elif isinstance(input_boundary.independent_query, EnvironmentTypedQuery):
            independent_types_schemas = [
                TypeResolver.get_document(input_boundary.independent_query.domain_type).model_json_schema()
            ]
        else:
            raise ShouldNotReachHereException(f"Unsupported query type {input_boundary.independent_query}")

        agent = Agent(
            model=self._model,
            instructions=f"""{self._prompt}
                       *schemas for types in dependent query: {dependent_types_schemas}
                       *schemas for types in independent: {independent_types_schemas}
                    """,
            output_type=SuggestEventCorrelationAIOutput,
        )
        response = await agent.run(
            user_prompt=input_boundary.model_dump_json(), output_type=SuggestEventCorrelationAIOutput
        )

        ai_output: SuggestEventCorrelationAIOutput = response.output
        now = datetime.now(tz=ZoneInfo("UTC"))
        td = TimeUtils.get_relativedelta_from_aggregation_interval(ai_output.temporal_options.time_delta)

        delta_from = TimeUtils.get_timedelta_from_aggregation_interval(ai_output.temporal_options.delta_from)
        delta_to = TimeUtils.get_timedelta_from_aggregation_interval(ai_output.temporal_options.delta_to)

        diff = delta_to - delta_from
        return SuggestEventCorrelationUseCaseOutputBoundary(
            temporal_options=CorrelationTemporalOptions(
                interval=TimeUtils.get_aggregation_interval_from_timedelta(diff),
                time_gte=now - td,
                time_lte=now,
                type=ai_output.temporal_options.type,
                delta_from=delta_from,
                delta_to=delta_to,
            ),
            dependent=CorrelationVariableInput(
                field_name=ai_output.dependent.field_name,
                query=input_boundary.dependent_query,
                aggregation_method=ai_output.dependent.aggregation_method,
            ),
            independent=CorrelationVariableInput(
                field_name=ai_output.independent.field_name,
                query=input_boundary.independent_query,
                aggregation_method=ai_output.independent.aggregation_method,
            ),
            reasoning=ai_output.reasoning,
        )
